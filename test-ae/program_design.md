# 需求规格说明书模板

## 文档信息

| 项目名称 | 【需求名称】     |
| ---- | ---------- |
| 文档版本 | V1.0       |
| 作者   | 孔镇浩        |
| 创建日期 | 2025-04-10 |
| 状态   | 已确认        |

## 变更履历

| 版本   | 日期         | 变更描述 | 修订人 | 审核  |
| ---- | ---------- | ---- | --- | --- |
| V1.0 | 2025-04-10 | 初次编写 | 孔镇浩 | 通过  |

## 1. 需求概述

### 1.1 需求背景

先忽略

### 1.2 需求目标

先忽略

### 1.3 需求范围

先忽略

### 1.4 相关干系人

| 角色    | 部门  | 姓名  | 职责  |
| ----- | --- | --- | --- |
| 产品负责人 |     |     |     |
| 业务负责人 |     |     |     |
| 技术负责人 |     |     |     |

## 2. 业务架构

### 2.1 业务模块关系图

无

### 2.2 模块列表

| 模块编号   | 模块名称 | 模块英文名                   | 英文缩写 |
| ------ | ---- | ----------------------- | ---- |
| MD0001 | 人工核保 | artificial underwriting | au   |

### 2.3 数据模型

#### 2.3.1 久期模块

##### 2.3.1.1 表间关系

##### 2.3.1.2 表名字典

| 表编号    | 表中文名    | 表英文名                                   | 备注  |
| ------ | ------- | -------------------------------------- | --- |
| TB0001 | 自动核保结论表 | t_4315_au_auto_underwriting_conclusion |     |

这里4315是应用编号，au是模块标识

##### 2.3.1.3 表集

**（1）TB0001**

CREATE TABLE `t_4315_au_auto_underwriting_conclusion` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id-主键id',
`name` varchar(100) COMMENT '名称-名称',
`owner_id` varchar(64) DEFAULT NULL COMMENT '归属人-归属人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间-创建时间',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间-更新时间',
`created_user` varchar(100) DEFAULT NULL COMMENT '创建人-创建人',
`modified_user` varchar(100) DEFAULT NULL COMMENT '修改人-修改人',
`yn` smallint(6) DEFAULT '1' COMMENT '是否有效-是否有效(1:有效,0:无效)',
`underwriting_date` date COMMENT '核保日期',
`underwriting_conclusion` varchar(64) COMMENT '核保结论',
`underwriting_rejection_reason` text COMMENT '未通过核保原因',
`insurance_policy_number` varchar(256) COMMENT '投保单号',
`underwriting_flow` varchar(64) COMMENT '核保流向',
`handler_person` varchar(256) COMMENT '处理人',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='自动核保结论';



### 2.4 用例列表

| 用例编号 | 用例名称 | 用例描述 | 模块编号 |
| ---- | ---- | ---- | ---- |
|      |      |      |      |
|      |      |      |      |

### 2.5 接口清单

| 接口编号   | 接口名称   | 接口描述 | 模块编号   |
| ------ | ------ | ---- | ------ |
| IF0001 | 提交核保结论 |      | MD0001 |

## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| 【通用术语1】 | 【定义】 | 【业务含义】 | 【备注】 |
| 【通用术语2】 | 【定义】 | 【业务含义】 | 【备注】 |

## 4. 功能需求

### 4.1 久期模块

#### 4.1.1 原型图

#### 4.1.2 接口功能

##### ******* 提交核保结论(FN0001)

##### *******.1 接口功能概述

提交核保结论

##### *******.2 接口基本信息

| 类型           | 描述                                                            |
| ------------ | ------------------------------------------------------------- |
| Method       | POST                                                          |
| Content-Type | multipart/form-data                                           |
| Url          | https://app-dima.idadt.com/znyf/auto_underwriting_conclusion/edit |

##### *******.3 接口入参

| 参数名  | 类型   | 是否必填 | 描述  |
| ---- | ---- | ---- | --- |
| id | Long | 是    | id   |
| name | String | 是    | 核保结论名称   |
| underwritingConclusion | String | 是    | 核保结论   |
| underwritingDate | Date | 是    | 核保结论日期   |
| underwritingFlow | String | 是    | 核保流向   |
| underwritingRejectionReason | String | 是    | 拒保原因   |

示例:
```json
{
    "id": 123456,
    "name": "核保1",
    "underwritingConclusion": "01",
    "underwritingDate": "2025-01-01",
    "underwritingFlow": "01",
    "underwritingRejectionReason": "不符合要求"
}
```

##### *******.4 接口出参

| 参数名   | 类型     | 描述             |
| ----- | ------ | -------------- |
| code  | Long   | 代码为200为成功,反则异常 |
| msg   | String | 异常描述           |

成功示例:
```json
{
    "code": 200,
    "msg": ""
}
```

失败示例:
```json
{
    "code": 500,
    "msg": "系统异常,请联系管理员!"
}
```

##### *******.5 接口功能详述

**步骤1：**
接收请求参数，并把通过id更新TB0001表