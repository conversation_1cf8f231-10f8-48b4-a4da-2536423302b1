package com.xinlong.dima.ae.controller;

import com.xinlong.dima.ae.dto.ApiResponse;
import com.xinlong.dima.ae.dto.AutoUnderwritingConclusionEditRequest;
import com.xinlong.dima.ae.service.AutoUnderwritingConclusionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 自动核保结论控制器
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-15
 */
@Log4j2
@RestController
@RequestMapping("/auto_underwriting_conclusion")
public class AutoUnderwritingConclusionController {

    @Resource
    private AutoUnderwritingConclusionService autoUnderwritingConclusionService;

    /**
     * 提交核保结论
     * 
     * @param request 编辑请求参数
     * @return 响应结果
     */
    @PostMapping("/edit")
    public ApiResponse<Void> editAutoUnderwritingConclusion(@Valid @RequestBody AutoUnderwritingConclusionEditRequest request) {
        try {
            log.info("接收到提交核保结论请求，参数: {}", request);
            
            boolean result = autoUnderwritingConclusionService.editAutoUnderwritingConclusion(request);
            
            if (result) {
                log.info("提交核保结论成功，id: {}", request.getId());
                return ApiResponse.success();
            } else {
                log.warn("提交核保结论失败，id: {}", request.getId());
                return ApiResponse.error("提交核保结论失败");
            }
        } catch (Exception e) {
            log.error("提交核保结论异常，参数: {}", request, e);
            return ApiResponse.error("系统异常,请联系管理员!");
        }
    }
}
