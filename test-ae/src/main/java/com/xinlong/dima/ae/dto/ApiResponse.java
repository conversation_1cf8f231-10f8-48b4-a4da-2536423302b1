package com.xinlong.dima.ae.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一API响应结果
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-15
 */
@Data
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应代码，200为成功，其他为异常
     */
    private Long code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 私有构造函数
     */
    private ApiResponse() {
    }

    /**
     * 私有构造函数
     */
    private ApiResponse(Long code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200L, "", null);
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200L, "", data);
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(String msg, T data) {
        return new ApiResponse<>(200L, msg, data);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String msg) {
        return new ApiResponse<>(500L, msg, null);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(Long code, String msg) {
        return new ApiResponse<>(code, msg, null);
    }
}
