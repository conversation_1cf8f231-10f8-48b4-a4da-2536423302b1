package com.xinlong.dima.ae.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinlong.dima.ae.dto.AutoUnderwritingConclusionEditRequest;
import com.xinlong.dima.ae.entity.AutoUnderwritingConclusion;

/**
 * 自动核保结论服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-15
 */
public interface AutoUnderwritingConclusionService extends IService<AutoUnderwritingConclusion> {

    /**
     * 编辑自动核保结论
     * 
     * @param request 编辑请求参数
     * @return 是否成功
     */
    boolean editAutoUnderwritingConclusion(AutoUnderwritingConclusionEditRequest request);
}
