package com.xinlong.dima.ae.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 自动核保结论实体类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_4315_au_auto_underwriting_conclusion")
@TableName("t_4315_au_auto_underwriting_conclusion")
public class AutoUnderwritingConclusion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @TableId(value = "id", type = IdType.AUTO)
    @Column(name = "id")
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    @Column(name = "name")
    private String name;

    /**
     * 归属人
     */
    @TableField("owner_id")
    @Column(name = "owner_id")
    private String ownerId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("created_user")
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 修改人
     */
    @TableField("modified_user")
    @Column(name = "modified_user")
    private String modifiedUser;

    /**
     * 是否有效(1:有效,0:无效)
     */
    @TableField("yn")
    @Column(name = "yn")
    private Integer yn;

    /**
     * 核保日期
     */
    @TableField("underwriting_date")
    @Column(name = "underwriting_date")
    private LocalDate underwritingDate;

    /**
     * 核保结论
     */
    @TableField("underwriting_conclusion")
    @Column(name = "underwriting_conclusion")
    private String underwritingConclusion;

    /**
     * 未通过核保原因
     */
    @TableField("underwriting_rejection_reason")
    @Column(name = "underwriting_rejection_reason")
    private String underwritingRejectionReason;

    /**
     * 投保单号
     */
    @TableField("insurance_policy_number")
    @Column(name = "insurance_policy_number")
    private String insurancePolicyNumber;

    /**
     * 核保流向
     */
    @TableField("underwriting_flow")
    @Column(name = "underwriting_flow")
    private String underwritingFlow;

    /**
     * 处理人
     */
    @TableField("handler_person")
    @Column(name = "handler_person")
    private String handlerPerson;
}
