package com.xinlong.dima.ae.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinlong.dima.ae.dto.AutoUnderwritingConclusionEditRequest;
import com.xinlong.dima.ae.entity.AutoUnderwritingConclusion;
import com.xinlong.dima.ae.mapper.AutoUnderwritingConclusionMapper;
import com.xinlong.dima.ae.service.AutoUnderwritingConclusionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 自动核保结论服务实现类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-15
 */
@Log4j2
@Service
public class AutoUnderwritingConclusionServiceImpl extends ServiceImpl<AutoUnderwritingConclusionMapper, AutoUnderwritingConclusion> 
        implements AutoUnderwritingConclusionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editAutoUnderwritingConclusion(AutoUnderwritingConclusionEditRequest request) {
        try {
            log.info("开始编辑自动核保结论，id: {}", request.getId());
            
            // 构建更新条件
            UpdateWrapper<AutoUnderwritingConclusion> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", request.getId());
            
            // 构建更新实体
            AutoUnderwritingConclusion updateEntity = new AutoUnderwritingConclusion();
            updateEntity.setName(request.getName());
            updateEntity.setUnderwritingConclusion(request.getUnderwritingConclusion());
            updateEntity.setUnderwritingDate(request.getUnderwritingDate());
            updateEntity.setUnderwritingFlow(request.getUnderwritingFlow());
            updateEntity.setUnderwritingRejectionReason(request.getUnderwritingRejectionReason());
            updateEntity.setUpdateTime(LocalDateTime.now());
            
            // 执行更新
            boolean result = this.update(updateEntity, updateWrapper);
            
            if (result) {
                log.info("编辑自动核保结论成功，id: {}", request.getId());
            } else {
                log.warn("编辑自动核保结论失败，id: {}", request.getId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("编辑自动核保结论异常，id: {}", request.getId(), e);
            throw new RuntimeException("编辑自动核保结论失败", e);
        }
    }
}
