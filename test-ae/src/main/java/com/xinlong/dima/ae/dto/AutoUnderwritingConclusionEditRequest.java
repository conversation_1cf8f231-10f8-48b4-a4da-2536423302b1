package com.xinlong.dima.ae.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 自动核保结论编辑请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-01-15
 */
@Data
public class AutoUnderwritingConclusionEditRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 核保结论名称
     */
    @NotBlank(message = "核保结论名称不能为空")
    private String name;

    /**
     * 核保结论
     */
    @NotBlank(message = "核保结论不能为空")
    private String underwritingConclusion;

    /**
     * 核保结论日期
     */
    @NotNull(message = "核保结论日期不能为空")
    private LocalDate underwritingDate;

    /**
     * 核保流向
     */
    @NotBlank(message = "核保流向不能为空")
    private String underwritingFlow;

    /**
     * 拒保原因
     */
    @NotBlank(message = "拒保原因不能为空")
    private String underwritingRejectionReason;
}
